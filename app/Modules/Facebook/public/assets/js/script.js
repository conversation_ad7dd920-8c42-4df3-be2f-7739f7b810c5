    $(document).on('change','#facebook_connection',function(e){
        var selectedIndex = this.selectedIndex;
        var selectedOption = this.options[selectedIndex];
        var token = selectedOption.getAttribute('data-token');
        document.getElementById('fbConnectionToken').value = token;
        document.getElementById('fbConnectionId').value = $(this).val();
        showLoader()
        getPages();
    })

    $(document).on('change','#facebook_page',function(e){
        var selectedIndex = this.selectedIndex;
        var selectedOption = this.options[selectedIndex];
        var token = selectedOption.getAttribute('data-token');
        var page_name = selectedOption.text;
        document.getElementById('tokenInput').value = token;
        document.getElementById('pageName').value = page_name;
        document.getElementById('pageInput').value = $(this).val();
        showLoader()
        getForms();
    })

    $(document).on('change','#lead_ad_form',function(e){
        var selectedIndex = this.selectedIndex;
        var selectedOption = this.options[selectedIndex];
        var ad_name = selectedOption.text;
        document.getElementById('adInput').value = $(this).val();
        document.getElementById('adName').value = ad_name;
        showLoader()
        getMapKeys();
    })   

    // Global variables for forms infinite loading
    var formsNextPage = null;
    var formsLoading = false;
    var allFormsData = [];

    function getForms(nextPage = null, isLoadMore = false){
        if (formsLoading) return; // Prevent multiple simultaneous requests

        formsLoading = true;
        token  = $('#tokenInput').val();
        page_id  = $('#pageInput').val();
        var destinationPath = '/api/facebook/get-forms';

        var requestData = {
            'page_id': page_id,
            'page_access_token': token
        };

        // Add next_page parameter if loading more data
        if (nextPage) {
            requestData.next_page = nextPage;
        }

        $.ajax({
            url: destinationPath,
            type: 'POST',
            data: requestData
        })
        .done(function(res) {
            if(res.status == true) {
                console.log("res.data",res.data);
                // Store the next_page value for future requests
                formsNextPage = res.next_page ?? null;

                if (!isLoadMore) {
                    // Initial load - reset the dropdown
                    allFormsData = res.data ?? [];
                    select = '<option value="">----- Choose ad forms -----</option>';

                    // Add all initial options
                    $.each(res.data, function (indexInArray, valueOfElement) {
                         select = select + '<option value="'+valueOfElement.id+'">'+valueOfElement.name+'</option>';
                    });

                    $('#lead_ad_form').html(select);
                } else {
                    // Load more - append to existing data
                    allFormsData = allFormsData.concat(res.data || []);

                    // Remove any existing "Load More" or completion indicators
                    $('#lead_ad_form option[value="load_more"]').remove();
                    $('#lead_ad_form option:disabled').remove();

                    // Add only the new options to the existing dropdown
                    $.each(res.data, function (indexInArray, valueOfElement) {
                         $('#lead_ad_form').append('<option value="'+valueOfElement.id+'">'+valueOfElement.name+'</option>');
                    });
                }

                // Add "Load More" option if there's more data
                if (formsNextPage) {
                    $('#lead_ad_form').append('<option value="load_more" class="load-more-option" style="background-color: #f8f9fa; font-style: italic; color: #007bff;">📄 Load More Forms (' + (allFormsData.length) + ' loaded)</option>');
                } else if (allFormsData.length > 0) {
                    // Add indicator showing total loaded forms when no more data
                    $('#lead_ad_form').append('<option disabled style="background-color: #e9ecef; font-style: italic; color: #6c757d;">✓ All ' + allFormsData.length + ' forms loaded</option>');
                }

                console.log('Forms loaded:', res.data.length, 'Total forms:', allFormsData.length, 'Has more:', !!formsNextPage, 'Load type:', isLoadMore ? 'append' : 'initial');
            } else {
                if (!isLoadMore) {
                    $('#lead_ad_form').html('<option value="">--- Choose ad forms ---</option>');
                }
            }
            formsLoading = false;
            hideLoader();
        })
        .fail(function(err) {
            console.error('Error loading forms:', err);
            formsLoading = false;
            hideLoader();
        });
    }

    // Handle "Load More" selection in forms dropdown
    $(document).on('change', '#lead_ad_form', function() {
        if ($(this).val() === 'load_more' && formsNextPage) {
            // Reset selection to empty
            $(this).val('');

            // Show loader and load more forms
            showLoader();
            getForms(formsNextPage, true);
        }
    });
    
    function getPages(){
        token = $('#fbConnectionToken').val();
        var destinationPath = '/api/facebook/get-pages';
        $.ajax({
            url: destinationPath,
            type: 'POST',
            data:{
                'access_token': token
            }
        })
        .done(function(res) {            
            if(res.status == true) {
                select = '<option value="">----- Choose ad pages -----</option>';
                $.each(res.data, function (indexInArray, valueOfElement) { 
                     select += '<option value="'+valueOfElement.id+'" data-token="'+valueOfElement.access_token+'">'+valueOfElement.name+'</option>';
                });
                $('#facebook_page').html(select); 
            } else {
                $('#facebook_page').html('<option value="">--- Choose ad pages ---</option>'); 
            }
            hideLoader();
        })
        .fail(function(err) {
        });
    }

    function getMapKeys(){
        token  = $('#tokenInput').val();
        page_id  = $('#pageInput').val();
        ad_id  = $('#adInput').val();
        var destinationPath = '/api/facebook/get-map-keys';
        $.ajax({
            url: destinationPath,
            type: 'POST',
            data:{
                'ad_id':ad_id,
                'page_id':page_id,
                'page_access_token': token
            }
        })
        .done(function(res) {  
            if(res.status == true) {
                sessionStorage.setItem('map_keys', JSON.stringify(res.data.field_data));
                let dropdown = '';
                $('.mp-dropdown-list').html('')
                $.each(res.data.field_data, function (indexInArray, valueOfElement) { 
                    dropdown += '<option value="'+indexInArray+'">'+valueOfElement.name+'</option>'
                });
                $('.mp-dropdown-list').html(dropdown)
            }

            hideLoader();
        })
        .fail(function(err) {
        });
    }

    function addCustomKeyValue() {
        const container = $('#custom-keys-container');
        html = '<div class="inline-form">\
                <input type="text" name="custom_keys[]" class="left form-control" placeholder="Key">\
                <input type="text" name="custom_values[]" class="right form-control" placeholder="Value" required>\
                <button type="button" onclick="removeCustomKeyValue(this)" class="form-control btn btn-info w-25"><i class="fa fa-trash"></i></button>\
            </div>';
        container.append(html);
    }

    function removeCustomKeyValue(button) {
        button.parentElement.remove();
    }

    function showLoader(){
        $('.loader-container').removeClass('d-none')
    }

    function hideLoader(){
        $('.loader-container').addClass('d-none')
    }
    function toggleResponseFields() {
        const leadForm = document.getElementById('lead_ad_form');
        const responseFields = document.getElementById('responseFields');
        if (leadForm.value) {
            responseFields.style.display = 'block';
        } else {
            responseFields.style.display = 'none';
        }
    }

    
    function reloadFacebookPages() {
        showLoader()
        getPages()
    }
    
    function reloadLeadForms() {
        // Reset forms data for fresh load
        formsNextPage = null;
        formsLoading = false;
        allFormsData = [];

        showLoader()
        getForms()
    }
    
    
    /* ------------ Map keys multiple add script --------*/

    function removeResponseField(button) {
        if ($('.response-row').length > 1) { // Only remove if more than one row exists
            $(button).closest('.response-row').remove(); // Remove the row
        }else{
            toastr.warning('Add atleast one item');
        }
    }


    // Remove tag when the 'x' is clicked
    $(document).on('click', '.remove-tag', function() {
        $(this).parent('.mp-tag').remove();
    });

    // Hide dropdown if clicking outside the container
    $(document).on('click', function(event) {
        if (!$(event.target).closest('.mp-input-container').length) {
            $('.mp-dropdown-list').hide();
        }
    });

    $(document).ready(function() {
        // Use event delegation to handle clicks on dynamically added .mp-text-input elements
        $(document).on('click', '.mp-text-input', function() {
            // Toggle the visibility of the sibling .mp-dropdown-list
            $(this).siblings('.mp-dropdown-list').toggle();
        });
    
        // Hide dropdown when clicking outside the .mp-input-container
        $(document).click(function(event) {
            if (!$(event.target).closest('.mp-input-container').length) {
                // Hide all .mp-dropdown-list elements
                $('.mp-dropdown-list').hide();
            }
        });
    
        // Update the text input with the selected dropdown option
        $(document).on('click', '.mp-dropdown-list option', function() {
            let selectedValue = $(this).text();
            let tag = `<span class="mp-tag" contenteditable="false">${selectedValue}<span class="remove-tag">&times;</span></span>&nbsp;`;
            // Set the selected value in the corresponding .mp-text-input
            $(this).closest('.mp-input-container').find('.mp-text-input').append(tag);
            // Hide the dropdown list after selection
            $(this).parent('.mp-dropdown-list').hide();
        });
    });