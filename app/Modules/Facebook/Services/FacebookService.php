<?php

namespace App\Modules\Facebook\Services;

use App\Modules\Facebook\Exceptions\UnableToGetLongLivedToken;
use App\Modules\Facebook\Exceptions\UnableToSubscribePage;
use App\Modules\Facebook\FacebookClient;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use App\Modules\Facebook\Repositories\FacebookRepository;
use JsonException;

class FacebookService
{
    public function __construct(
        private readonly FacebookRepository $facebookRepository,
        private readonly FacebookClient $facebookClient
    ) {
    }

    public function loginFacebook(): RedirectResponse
    {
        try {
            return $this->facebookClient->login();
        } catch (Exception $e) {
            Log::info('Facebook login error', ['error' => $e->getMessage()]);
            return redirect()->back();
        }
    }

    public function getUser()
    {
        return $this->facebookClient->user();
    }

    public function storeWorkflow(string $workFlowName)
    {
        return $this->facebookRepository->storeWorkflow($workFlowName);
    }

    public function changeWorkflowStatus($data)
    {
        return $this->facebookRepository->changeWorkflowStatus($data);
    }

    /**
     * @return string[]
     * @throws JsonException
     */
    public function listFbPage(string $accessToken): array
    {
        try {
            $response = $this->facebookClient->listPages(accessToken: $accessToken);

            return $response['data'] ?? [];
        } catch (GuzzleException $e) {
            Log::info('Facebook list pages error', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * @throws UnableToSubscribePage
     */
    public function subscribePage(string $pageId, string $accessToken): void
    {
        try {
            $this->facebookClient->subscribe(accessToken: $accessToken, pageId: $pageId);
        } catch (GuzzleException $e) {
            Log::info('Facebook subscribe error', ['error' => $e->getMessage()]);
            throw UnableToSubscribePage::forPageId($pageId);
        }
    }

    /**
     * @throws JsonException
     */
    public function listAds(string $pageId, string $accessToken, ?string $nextPage = null): array
    {
        try {
            $response = $this->facebookClient->listAds($accessToken, $pageId, $nextPage);

            return [
                'data' => $response['data'] ?? [],
                'next_page' => $response['paging']['cursors']['after'] ?? null,
            ];
        } catch (GuzzleException $e) {
            Log::info('Facebook list ads error', ['error' => $e->getMessage()]);
            return [
                'data' => [],
                'next_page' => null,
            ];
        }
    }

    /**
     * @throws JsonException
     */
    public function fetchSampleFormResponse(int $pageId, string $accessToken): array
    {
        try {
            $response = $this->facebookClient->sampleFormResponse($accessToken, $pageId);

            return $response['data'] ?? [];
        } catch (GuzzleException $e) {
            Log::info('Facebook sample form response error', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * @throws JsonException
     */
    public function fetchLeadInformation(string $leadgenId, string $accessToken): array
    {
        try {
            $response = $this->facebookClient->fetchLeadInformation($leadgenId, $accessToken);

            return $response;
        } catch (GuzzleException $e) {
            Log::info('Facebook sample form response error', ['error' => $e->getMessage()]);
            return [];
        }
    }

    public function getPage($page_id)
    {
        return $this->facebookRepository->getPage($page_id);
    }

    public function checkWorkFlow($data)
    {
        return $this->facebookRepository->workFlow($data);
    }

    public function saveMappedKeys($data)
    {
        return $this->facebookRepository->saveMap($data);
    }

    public function saveToCrm($leadDetails, $workFlow, $adMetaData, $vendorId)
    {
        $this->facebookRepository->recordLead($leadDetails, $workFlow, $adMetaData, $vendorId);
    }

    public function getWorkflow($id = null)
    {
        return $this->facebookRepository->getWorkflow($id);
    }

    /**
     * @throws JsonException
     * @throws UnableToGetLongLivedToken
     */
    public function getLongLivedToken(string $shortLivedToken): string
    {
        try {
            $response = $this->facebookClient->getLongLivedToken($shortLivedToken);

            return $response['access_token'] ?? '';
        } catch (GuzzleException $e) {
            Log::info('Facebook get long lived token error', ['error' => $e->getMessage()]);
            throw UnableToGetLongLivedToken::withShortLivedToken($shortLivedToken);
        }
    }
}
