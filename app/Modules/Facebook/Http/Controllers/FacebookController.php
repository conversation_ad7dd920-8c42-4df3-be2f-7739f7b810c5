<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Http\Controllers;

use App\Modules\Facebook\Services\FacebookService;
use App\Modules\Facebook\Traits\FacebookTrait;
use App\User;
use Exception;
use App\FrontendModel\LeadAdditionalField;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Throwable;

class FacebookController
{
    use FacebookTrait;

    protected $vendorId;

    protected $helper;

    protected $vendor;

    public function __construct(
        private readonly FacebookService $facebookService
    ) {
        // Session::start();

        // $this->vendorId = User::getVendorId();
        // $this->vendor = $this->getFacebookVendor();
    }

    public function login(): RedirectResponse
    {
        Log::info('Facebook login attempting');
        return $this->facebookService->loginFacebook();
    }

    public function callback(): RedirectResponse
    {
        try {
            $lastVisitUrl = $this->vendorId . ' last_url_visited';

            Log::info('Facebook callback attempting');
            $user = $this->facebookService->getUser();

            Log::info('Facebook callback user', [
                'user' => $user,
            ]);

            Log::info('Facebook fetching long lived token');
            $longLivedToken = $this->facebookService->getLongLivedToken($user->token);

            if (!$longLivedToken) {
                return redirect(session($lastVisitUrl) ?? 'v1/facebook')->with('error', 'Error: ' . $e->getMessage());
            }

            Log::info('Facebook fetched long lived token', [
                'long_lived_token' => $longLivedToken,
            ]);

            // Save or update the vendor's Facebook token
            $this->saveVendorAccessToken(accessToken: $longLivedToken);

            Log::info('Successfully logged in with Facebook');
            return redirect(session($lastVisitUrl) ?? 'v1/facebook')->with(
                'success',
                'Successfully logged in with Facebook'
            );
        } catch (Exception $e) {
            Log::info('Facebook connection error occurred: ', [
                'error' => $e->getMessage(),
                'context' => $e->getTrace(),
            ]);
            $lastVisitUrl = $this->vendorId . ' last_url_visited';
            return redirect(session($lastVisitUrl) ?? 'v1/facebook')->with('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * @throws \JsonException
     */
    public function pageList(): JsonResponse
    {
        Log::info('Facebook page list attempting');

        $accessToken = request('access_token');
        $pages = $this->facebookService->listFbPage(accessToken: $accessToken);

        return response()->json([
            'message' => 'list pages',
            'status' => true,
            'data' => $pages,
        ]);
    }

    public function formList(): JsonResponse
    {
        $pageId = request('page_id');
        $accessToken = request('page_access_token');
        $nextPage = request('next_page', null);

        Log::withContext([
            'pageId' => $pageId,
            'accessToken' => $accessToken,
        ]);

        try {
            if($nextPage === null) {
                Log::info('Facebook subscribe page attempting');
                // Subscribe to the page
                $this->facebookService->subscribePage(pageId: $pageId, accessToken: $accessToken);   
            }

            Log::info('Facebook list ads attempting');
            $adsResponse = $this->facebookService->listAds(pageId: $pageId, accessToken: $accessToken, nextPage: $nextPage);

            return response()->json([
                'message' => 'list pages',
                'status' => true,
                'data' => $adsResponse['data'] ?? [],
                'next_page' => $adsResponse['next_page'] ?? null,
            ]);
        } catch (Exception $e) {
            Log::info('Facebook form list', ['error' => $e->getMessage()]);
            return response()->json([
                'message' => 'Failed to list pages',
                'status' => false,
            ], 422);
        }
    }

    public function create(): View
    {
        Log::info('Facebook create');
        // list exisitng connections
        $connections = $this->getFacebookConnections();
        Log::info('Facebook create connections', [
            'connections' => $connections,
        ]);
        $lastVisitUrl = $this->vendorId . ' last_url_visited';

        session([
            $lastVisitUrl => request()->url(),
        ]);
        $additional_fields =  LeadAdditionalField::where('vendor_id', User::getVendorId())->get();

        return view('facebook::create', compact('connections','additional_fields'));
    }

    public function workflowMapKeys($work_flow_id)
    {
        $id = $this->decryptId($work_flow_id);
        Log::info('Facebook workflow map keys attempting');
        $workflow = $this->facebookService->getWorkflow($id)->first();
        $pageId = $workflow->fb_page_id;
        $last_visit_url = $this->vendorId . ' last_url_visited';
        session([
            $last_visit_url => request()->url(),
        ]);

        // list exisitng connections
        $connections = $this->getFacebookConnections();

        Log::info('Facebook workflow map keys connections', [
            'connections' => $connections,
        ]);

        // Get map details
        $mapped_keys = $workflow->mapped_keys;

        // Get sample response
        try {
            $adResponse = $this->facebookService->fetchSampleFormResponse(
                pageId: $workflow->fb_ad_id,
                accessToken: $workflow->page_access_token
            );
            Log::info('Facebook workflow map keys ad response', [
                'ad_response' => $adResponse,
            ]);
            $adResponseData = $this->getLeaddata($adResponse, 'sample_data');
            if (empty($adResponseData)) {
                $adResponseData['field_data'] = [];
            }
        } catch (Throwable $th) {
            Log::info('Facebook workflow map keys', [
                'reason' => $th->getMessage(),
            ]);
            $adResponseData['field_data'] = [];
        }
        $additional_fields =  LeadAdditionalField::where('vendor_id', User::getVendorId())->get();

        return view('facebook::map-facebook-keys', compact(
            'workflow',
            'work_flow_id',
            'connections',
            'mapped_keys',
            'adResponseData',
            'additional_fields'
        ));
    }

    /**
     * @throws \JsonException
     */
    public function getMapKeys(): JsonResponse
    {
        $adId = request('ad_id');
        $accessToken = request('page_access_token');

        Log::info('Facebook get map keys attempting');
        // Fetch sample response from the ad
        $adResponse = $this->facebookService->fetchSampleFormResponse(pageId: (int) $adId, accessToken: $accessToken);
        Log::info('Facebook get map keys', [
            'ad_response' => $adResponse,
        ]);

        $adResponseData = $this->getLeaddata($adResponse,'sample_data');
        $leadResponse = $this->getLeaddata($adResponse,'actual_data'); // Fetch the lead data using your logic

        if(collect($leadResponse)->has('ad_name'))
        {
            $sdata=['name'=>"source",'value'=>['0'=>"Facebook"]];
            $adname=['name'=>"ad_name",'value'=>['0'=> $leadResponse['ad_name']]];
            $adata=['name'=>"adset_name",'value'=>['0'=>$leadResponse['adset_name']]];
            $cdata=['name'=>"campaign_name",'value'=>['0'=>$leadResponse['campaign_name']]];

            $adResponseData['field_data'][]=$sdata;
            $adResponseData['field_data'][]=$adname;
            $adResponseData['field_data'][]=$adata;
            $adResponseData['field_data'][]=$cdata;

            $leadResponse['field_data'][]=$sdata;
            $leadResponse['field_data'][]=$adname;
            $leadResponse['field_data'][]=$adata;
            $leadResponse['field_data'][]=$cdata;
        }

        Log::info('Facebook get map keys', [
            'ad_response_data' => $adResponseData,
            'lead_response' => $leadResponse,
        ]);

        return response()->json([
            'message' => 'list map keys',
            'status' => true,
            'data' => $adResponseData,
            'leadResponse' => $leadResponse
        ],200);
    }

    public function getConnections(): JsonResponse
    {
        // list exisitng connections
        $connections = $this->getFacebookConnections();
        Log::info('Facebook get facebook connections', [
            'connections' => $connections,
        ]);
        return response()->json([
            'message' => 'connection lists',
            'status' => true,
            'data' => $connections,
        ], 200);
    }

    public function deleteConnection($id)
    {
        $connection = $this->deleteFacebookConnection($id);
        if ($connection) {
            return response()->json([
                'message' => 'Successfully deleted',
                'status' => true,
            ], 200);
        }

        return response()->json([
            'message' => 'Delete failed',
            'status' => false,
        ], 200);
    }

    public function updateConnection($id)
    {
        $name = request('name');
        $connection = $this->updateFacebookConnection($id, $name);
        if ($connection) {
            return response()->json([
                'message' => 'Successfully updated',
                'status' => true,
            ], 200);
        }

        return response()->json([
            'message' => 'Update failed',
            'status' => false,
        ], 200);
    }

    public function submitMapKeys()
    {
        try {
            $data['adId'] = request('ad_id');
            $data['form_name'] = request('ad_name');
            $data['page_id'] = request('page_id');
            $data['page_name'] = request('page_name');
            $data['work_flow_id'] = request('work_flow_id');
            $data['page_access_token'] = request('token');
            $data['fb_connection_token'] = request('fb_connection_token');
            $data['fb_connection_id'] = request('fb_connection_id');
            $data['adgroup_id'] = request('adgroup_id') ?? '';
            $data['active'] = request('checkValue') == '1' || request('checkValue') == 'true' ? 1 : 0;
            $data['workflow_name'] = request('workflow_name');
            $customField = json_decode(request('customData'));

            $data['maped_keys'] = $customField;
            $data['vendor_id'] = $this->vendorId;
          
            Log::info('Facebook save workflow', [
                'data' => $data,
            ]);

            // Save pages
            $this->saveFacebookPage($data);

            // save form
            $this->saveFacebookAd($data);

            // save mapped keys
            $workFlowData = $this->facebookService->saveMappedKeys($data);

            session()->put([
                'success' => 'Workflow saved successfully!',
            ]);

            return redirect('v1/facebook/map-keys/' . $this->encryptId($workFlowData->id));
        } catch (Exception $e) {
            session()->put([
                'success' => 'Something went wrong!',
            ]);
            Log::info($e);
            return redirect()->back();
        }

    }
}
