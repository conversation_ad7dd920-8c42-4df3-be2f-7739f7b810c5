<style>
.form-container {
    max-width: 500px;
    margin: auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: white;
}

.help-text {
    font-size: 0.9em;
    color: #555;
}
.btn-custom {
    width: 100%;
    margin-bottom: 10px;
}

label {
    display: block;
    margin-bottom: 5px;
}

.btn-fb {
    cursor: pointer;
    background-color: #007bff;
    color: white;
    border: none;
}
.btn-fb:hover {
    background-color: #0056b3;
}
.help-text {
    font-size: 0.9em;
    color: #555;
}

.inline-form {
    display: flex;
    align-items: center;
}
.inline-form input {
    margin-right: 10px; /* Adjust spacing between inputs */
}
.inline-form button {
    margin-left: 10px; /* Adjust spacing between button and inputs */
}
.fa.fa-plus:hover {
    animation: none !important;
}
.loader-container {
    display: flex;
    justify-content: center; /* Aligns horizontally */
    align-items: center;     /* Aligns vertically */
    position: absolute;      /* Make sure the container has relative positioning */
    height: 417px;           /* Set the height of the container */
    width: 32%;             /* Full width of the parent */
}

.centered-image {
    width: 100px;            /* Image width */
    height: auto;            /* Maintain aspect ratio */
    position: absolute;
    z-index: 999999;         /* Ensure it appears above other elements */
}

.name-wf:hover{
    color: #f4516c;
}

/* custom check box */
.custom-control-label::before {
    border-radius: 34px;
    width: 60px;
    height: 34px;
    background-color: #ccc;
}

.custom-control-label::after {
    border-radius: 50% !important;
    height: 26px !important;
    width: 26px !important;
    background-color: #fff !important;
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #4CAF50;
}

.custom-control-input:checked ~ .custom-control-label::after {
    transform: translateX(26px);
}
.custom-switch label::after {
    left: 4px !important;
}

/* Dropdown infinite loading styles */
.load-more-option {
    background-color: #f8f9fa !important;
    color: #007bff !important;
    font-style: italic !important;
    border-top: 1px solid #dee2e6 !important;
}

.all-loaded-option {
    background-color: #e9ecef !important;
    color: #6c757d !important;
    font-style: italic !important;
    border-top: 1px solid #dee2e6 !important;
}

/* Forms loaded notification */
.forms-loaded-notification {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    display: flex;
    align-items: center;
    gap: 8px;
}

.forms-loaded-notification i {
    font-size: 16px;
}
</style>